# Web Application Template - Environment Variables
# Copy this file to .env and update the values

# Flask Configuration
SECRET_KEY=your-secret-key-change-this-in-production
FLASK_DEBUG=False
FLASK_ENV=production

# Server Configuration
PORT=5000
HOST=0.0.0.0

# Database Configuration
DATABASE_URL=sqlite:///app.db
# For PostgreSQL: postgresql://username:password@localhost/dbname
# For MySQL: mysql://username:password@localhost/dbname

# File Upload Configuration
UPLOAD_FOLDER=static/uploads
MAX_CONTENT_LENGTH=16777216  # 16MB in bytes

# API Keys (add your API keys here)
# OPENAI_API_KEY=your-openai-api-key
# GOOGLE_API_KEY=your-google-api-key
# STRIPE_API_KEY=your-stripe-api-key

# Email Configuration (if using email features)
# MAIL_SERVER=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USE_TLS=True
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password

# Redis Configuration (if using Redis)
# REDIS_URL=redis://localhost:6379/0

# Security Settings
# SESSION_COOKIE_SECURE=True  # Set to True in production with HTTPS
# SESSION_COOKIE_HTTPONLY=True
# SESSION_COOKIE_SAMESITE=Lax

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=app.log

# Application Specific Settings
APP_NAME=My Web App
APP_VERSION=1.0.0
