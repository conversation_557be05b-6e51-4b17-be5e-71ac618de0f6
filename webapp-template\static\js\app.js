// Custom JavaScript for Web Application Template

// Global app object
const App = {
    // Configuration
    config: {
        apiBaseUrl: '/api',
        uploadMaxSize: 16 * 1024 * 1024, // 16MB
        autoRefreshInterval: 300000 // 5 minutes
    },

    // Initialize the application
    init: function() {
        console.log('Initializing Web Application Template');
        this.setupEventListeners();
        this.setupTooltips();
        this.setupAutoRefresh();
        this.addAnimations();
    },

    // Setup global event listeners
    setupEventListeners: function() {
        // Handle form submissions
        document.addEventListener('submit', this.handleFormSubmit.bind(this));
        
        // Handle file uploads
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => {
            input.addEventListener('change', this.handleFileSelect.bind(this));
        });

        // Handle AJAX requests
        document.addEventListener('click', (e) => {
            if (e.target.hasAttribute('data-ajax-url')) {
                e.preventDefault();
                this.makeAjaxRequest(e.target.getAttribute('data-ajax-url'));
            }
        });

        // Handle modal events
        document.addEventListener('show.bs.modal', this.handleModalShow.bind(this));
        document.addEventListener('hidden.bs.modal', this.handleModalHidden.bind(this));
    },

    // Setup Bootstrap tooltips
    setupTooltips: function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },

    // Setup auto-refresh for dynamic content
    setupAutoRefresh: function() {
        if (window.location.pathname === '/dashboard') {
            setInterval(() => {
                this.refreshDashboardData();
            }, this.config.autoRefreshInterval);
        }
    },

    // Add entrance animations to elements
    addAnimations: function() {
        const animatedElements = document.querySelectorAll('.card, .alert, .jumbotron');
        animatedElements.forEach((element, index) => {
            element.style.animationDelay = `${index * 0.1}s`;
            element.classList.add('fade-in');
        });
    },

    // Handle form submissions
    handleFormSubmit: function(e) {
        const form = e.target;
        if (form.hasAttribute('data-ajax')) {
            e.preventDefault();
            this.submitFormAjax(form);
        }
    },

    // Handle file selection
    handleFileSelect: function(e) {
        const file = e.target.files[0];
        if (file) {
            if (file.size > this.config.uploadMaxSize) {
                this.showAlert('File too large. Maximum size is 16MB.', 'error');
                e.target.value = '';
                return;
            }
            this.validateFileType(file, e.target);
        }
    },

    // Validate file type
    validateFileType: function(file, input) {
        const allowedTypes = input.getAttribute('data-allowed-types');
        if (allowedTypes) {
            const types = allowedTypes.split(',');
            const fileExtension = file.name.split('.').pop().toLowerCase();
            if (!types.includes(fileExtension)) {
                this.showAlert(`File type not allowed. Allowed types: ${types.join(', ')}`, 'error');
                input.value = '';
                return false;
            }
        }
        return true;
    },

    // Submit form via AJAX
    submitFormAjax: function(form) {
        const formData = new FormData(form);
        const url = form.action || window.location.pathname;
        
        this.showLoading(true);
        
        fetch(url, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            this.handleAjaxResponse(data);
            if (data.success) {
                form.reset();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            this.showAlert('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            this.showLoading(false);
        });
    },

    // Make AJAX request
    makeAjaxRequest: function(url, method = 'GET', data = null) {
        this.showLoading(true);
        
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };
        
        if (data) {
            options.body = JSON.stringify(data);
        }
        
        fetch(url, options)
        .then(response => response.json())
        .then(data => {
            this.handleAjaxResponse(data);
        })
        .catch(error => {
            console.error('Error:', error);
            this.showAlert('An error occurred. Please try again.', 'error');
        })
        .finally(() => {
            this.showLoading(false);
        });
    },

    // Handle AJAX response
    handleAjaxResponse: function(data) {
        if (data.message) {
            this.showAlert(data.message, data.success ? 'success' : 'error');
        }
        
        if (data.redirect) {
            window.location.href = data.redirect;
        }
        
        if (data.reload) {
            window.location.reload();
        }
    },

    // Show alert message
    showAlert: function(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container') || this.createAlertContainer();
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        alertContainer.appendChild(alertDiv);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    },

    // Create alert container if it doesn't exist
    createAlertContainer: function() {
        const container = document.createElement('div');
        container.id = 'alert-container';
        container.className = 'position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1050';
        document.body.appendChild(container);
        return container;
    },

    // Show/hide loading indicator
    showLoading: function(show) {
        let loader = document.getElementById('global-loader');
        
        if (show && !loader) {
            loader = document.createElement('div');
            loader.id = 'global-loader';
            loader.className = 'position-fixed top-50 start-50 translate-middle';
            loader.style.zIndex = '1060';
            loader.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            `;
            document.body.appendChild(loader);
        } else if (!show && loader) {
            loader.remove();
        }
    },

    // Handle modal show event
    handleModalShow: function(e) {
        const modal = e.target;
        const form = modal.querySelector('form');
        if (form) {
            form.reset();
        }
    },

    // Handle modal hidden event
    handleModalHidden: function(e) {
        const modal = e.target;
        // Clear any validation errors
        const errorElements = modal.querySelectorAll('.is-invalid');
        errorElements.forEach(element => {
            element.classList.remove('is-invalid');
        });
    },

    // Refresh dashboard data
    refreshDashboardData: function() {
        fetch('/api/data')
        .then(response => response.json())
        .then(data => {
            this.updateDashboardMetrics(data);
        })
        .catch(error => {
            console.error('Error refreshing dashboard:', error);
        });
    },

    // Update dashboard metrics
    updateDashboardMetrics: function(data) {
        // Update metric cards
        const metricElements = document.querySelectorAll('[data-metric]');
        metricElements.forEach(element => {
            const metric = element.getAttribute('data-metric');
            if (data[metric] !== undefined) {
                element.textContent = data[metric];
            }
        });
    },

    // Utility functions
    utils: {
        // Format file size
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        // Format date
        formatDate: function(date) {
            return new Date(date).toLocaleDateString();
        },

        // Format datetime
        formatDateTime: function(date) {
            return new Date(date).toLocaleString();
        },

        // Debounce function
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Generate random ID
        generateId: function() {
            return Math.random().toString(36).substr(2, 9);
        }
    }
};

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    App.init();
});

// Export for use in other scripts
window.App = App;
