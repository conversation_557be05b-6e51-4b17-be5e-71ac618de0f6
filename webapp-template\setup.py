#!/usr/bin/env python3
"""
Web Application Template - Setup Script
Helps set up a new project from the template
"""

import os
import sys
import shutil
import secrets
import subprocess
from pathlib import Path

def generate_secret_key():
    """Generate a secure secret key"""
    return secrets.token_urlsafe(32)

def create_env_file():
    """Create .env file from template"""
    if os.path.exists('.env'):
        print("✅ .env file already exists")
        return
    
    if not os.path.exists('.env.example'):
        print("❌ .env.example not found")
        return
    
    # Read template
    with open('.env.example', 'r') as f:
        content = f.read()
    
    # Replace placeholder secret key
    secret_key = generate_secret_key()
    content = content.replace('your-secret-key-change-this-in-production', secret_key)
    
    # Write .env file
    with open('.env', 'w') as f:
        f.write(content)
    
    print("✅ Created .env file with secure secret key")

def setup_virtual_environment():
    """Set up Python virtual environment"""
    if os.path.exists('venv'):
        print("✅ Virtual environment already exists")
        return
    
    print("🔧 Creating virtual environment...")
    try:
        subprocess.run([sys.executable, '-m', 'venv', 'venv'], check=True)
        print("✅ Virtual environment created")
        
        # Determine activation script path
        if os.name == 'nt':  # Windows
            activate_script = 'venv\\Scripts\\activate'
            pip_path = 'venv\\Scripts\\pip'
        else:  # Unix/Linux/macOS
            activate_script = 'venv/bin/activate'
            pip_path = 'venv/bin/pip'
        
        print(f"📝 To activate: {activate_script}")
        
        # Install requirements
        if os.path.exists('requirements.txt'):
            print("📦 Installing requirements...")
            subprocess.run([pip_path, 'install', '-r', 'requirements.txt'], check=True)
            print("✅ Requirements installed")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error setting up virtual environment: {e}")

def customize_project():
    """Interactive project customization"""
    print("\n🎨 Project Customization")
    print("=" * 30)
    
    # Get project details
    app_name = input("Enter your app name (default: My Web App): ").strip()
    if not app_name:
        app_name = "My Web App"
    
    description = input("Enter app description (optional): ").strip()
    
    # Update app.py
    if os.path.exists('app.py'):
        with open('app.py', 'r') as f:
            content = f.read()
        
        # Replace app name in context processor
        content = content.replace("'app_name': 'My Web App'", f"'app_name': '{app_name}'")
        
        with open('app.py', 'w') as f:
            f.write(content)
        
        print(f"✅ Updated app name to: {app_name}")
    
    # Update README.md
    if os.path.exists('README.md') and description:
        with open('README.md', 'r') as f:
            content = f.read()
        
        # Add description after the title
        content = content.replace(
            'A comprehensive Flask web application template with modern features and best practices.',
            description
        )
        
        with open('README.md', 'w') as f:
            f.write(content)
        
        print(f"✅ Updated README with description")

def main():
    """Main setup function"""
    print("🚀 Web Application Template Setup")
    print("=" * 40)
    
    # Check if we're in the right directory
    if not os.path.exists('app.py'):
        print("❌ app.py not found. Make sure you're in the template directory.")
        sys.exit(1)
    
    print("Setting up your new web application...\n")
    
    # Setup steps
    try:
        # 1. Create environment file
        print("1️⃣ Setting up environment...")
        create_env_file()
        
        # 2. Setup virtual environment
        print("\n2️⃣ Setting up Python environment...")
        setup_virtual_environment()
        
        # 3. Customize project
        print("\n3️⃣ Customizing project...")
        customize_project()
        
        # 4. Final instructions
        print("\n🎉 Setup Complete!")
        print("=" * 20)
        print("\nNext steps:")
        print("1. Activate virtual environment:")
        if os.name == 'nt':  # Windows
            print("   venv\\Scripts\\activate")
        else:  # Unix/Linux/macOS
            print("   source venv/bin/activate")
        
        print("2. Run the application:")
        print("   python app.py")
        print("   # or")
        print("   python run.py")
        
        print("3. Open http://localhost:5000 in your browser")
        
        print("\n📚 For more information, see README.md")
        
    except KeyboardInterrupt:
        print("\n\n👋 Setup cancelled by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
