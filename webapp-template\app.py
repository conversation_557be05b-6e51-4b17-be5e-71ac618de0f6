#!/usr/bin/env python3
"""
Universal Web Application Template - Flask Application
A flexible, reusable template for any type of web application
"""

import os
import json
import uuid
from datetime import datetime
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, session
from werkzeug.utils import secure_filename

# Initialize Flask app
app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'change-this-secret-key-in-production')

# Configuration - easily customizable for any project type
app.config.update({
    'UPLOAD_FOLDER': 'static/uploads',
    'MAX_CONTENT_LENGTH': 16 * 1024 * 1024,  # 16MB max file size
    'DATABASE_URL': os.environ.get('DATABASE_URL', 'sqlite:///app.db'),
    'DEBUG': os.environ.get('FLASK_DEBUG', 'False').lower() == 'true'
})

# Ensure required directories exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('data', exist_ok=True)

# Allowed file extensions for uploads
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp', 'pdf', 'txt', 'csv'}

def allowed_file(filename):
    """Check if uploaded file has allowed extension"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Custom template filters
@app.template_filter('safe_json')
def safe_json_filter(obj):
    """Safely convert object to JSON string for templates"""
    try:
        if hasattr(obj, 'to_dict'):
            return json.dumps(obj.to_dict())
        elif hasattr(obj, '__dict__'):
            return json.dumps(obj.__dict__)
        else:
            return json.dumps(obj)
    except Exception as e:
        print(f"JSON serialization error: {e}")
        return '{}'

@app.template_filter('format_datetime')
def format_datetime_filter(dt):
    """Format datetime for display"""
    if isinstance(dt, str):
        try:
            dt = datetime.fromisoformat(dt)
        except:
            return dt
    return dt.strftime('%Y-%m-%d %H:%M') if dt else ''

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('errors/500.html'), 500

@app.errorhandler(413)
def too_large(error):
    flash('File too large. Maximum size is 16MB.', 'error')
    return redirect(request.url)

# Main routes
@app.route('/')
def index():
    """Home page"""
    return render_template('index.html', 
                         title='Home',
                         current_time=datetime.now())

@app.route('/about')
def about():
    """About page"""
    return render_template('about.html', title='About')

@app.route('/dashboard')
def dashboard():
    """Dashboard page - customize based on your app needs"""
    # Add your dashboard logic here
    data = {
        'total_items': 0,
        'recent_activity': [],
        'stats': {}
    }
    return render_template('dashboard.html', 
                         title='Dashboard',
                         data=data)

# API routes
@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0'
    })

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """Handle file uploads"""
    if 'file' not in request.files:
        return jsonify({'error': 'No file provided'}), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400
    
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        # Add timestamp to avoid conflicts
        name, ext = os.path.splitext(filename)
        filename = f"{name}_{int(datetime.now().timestamp())}{ext}"
        
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        return jsonify({
            'success': True,
            'filename': filename,
            'url': url_for('static', filename=f'uploads/{filename}')
        })
    
    return jsonify({'error': 'Invalid file type'}), 400

@app.route('/api/data', methods=['GET', 'POST'])
def handle_data():
    """Generic data handling endpoint - customize for your needs"""
    if request.method == 'POST':
        data = request.get_json()
        # Process and save data here
        return jsonify({'success': True, 'message': 'Data saved'})
    else:
        # Return data here
        return jsonify({'data': [], 'total': 0})

# Utility functions
def save_json_data(filename, data):
    """Save data to JSON file"""
    filepath = os.path.join('data', filename)
    with open(filepath, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)

def load_json_data(filename, default=None):
    """Load data from JSON file"""
    filepath = os.path.join('data', filename)
    if os.path.exists(filepath):
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading {filename}: {e}")
    return default or {}

def generate_id():
    """Generate unique ID"""
    return str(uuid.uuid4())

# Context processors (available in all templates)
@app.context_processor
def inject_globals():
    """Inject global variables into templates"""
    return {
        'app_name': 'My Web App',
        'current_year': datetime.now().year,
        'version': '1.0.0'
    }

if __name__ == '__main__':
    # Development server
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    print(f"Starting server on port {port}")
    print(f"Debug mode: {debug}")
    
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug,
        threaded=True
    )
