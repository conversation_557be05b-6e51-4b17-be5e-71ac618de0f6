# Web Application Template

A comprehensive Flask web application template with modern features and best practices. Perfect for quickly starting new web projects.

## Features

- 🚀 **Flask Web Framework** - Modern Python web development
- 🎨 **Bootstrap 5 UI** - Responsive and beautiful interface
- 📁 **File Upload System** - Secure file handling with validation
- 📊 **Dashboard with Charts** - Interactive data visualization
- 🔧 **JSON Data Storage** - Simple data persistence
- 🐳 **Docker Ready** - Easy deployment and development
- 📱 **Responsive Design** - Works on all devices
- 🔒 **Security Features** - CSRF protection, secure uploads
- 🎯 **API Endpoints** - RESTful API structure
- ⚡ **Modern JavaScript** - ES6+ with utilities

## Quick Start

### 1. <PERSON><PERSON> or Co<PERSON> the Template

```bash
# Copy the webapp-template folder to your new project
cp -r webapp-template my-new-project
cd my-new-project
```

### 2. Set Up Environment

```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Configure Environment Variables

```bash
# Copy environment template
cp .env.example .env

# Edit .env file with your settings
# At minimum, change the SECRET_KEY
```

### 4. Run the Application

```bash
# Development mode
python app.py

# Or with Flask CLI
flask run
```

Visit `http://localhost:5000` to see your application!

## Docker Deployment

### Using Docker Compose (Recommended)

```bash
# Build and run
docker-compose up -d

# View logs
docker-compose logs -f

# Stop
docker-compose down
```

### Using Docker directly

```bash
# Build image
docker build -t my-webapp .

# Run container
docker run -p 5000:5000 my-webapp
```

## Project Structure

```
webapp-template/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── .env.example          # Environment variables template
├── Dockerfile            # Docker configuration
├── docker-compose.yml    # Docker Compose setup
├── README.md             # This file
├── templates/            # HTML templates
│   ├── base.html         # Base template with navigation
│   ├── index.html        # Home page
│   ├── dashboard.html    # Dashboard with charts
│   ├── about.html        # About page
│   └── errors/           # Error pages (404, 500)
├── static/               # Static files
│   ├── css/
│   │   └── style.css     # Custom CSS styles
│   ├── js/
│   │   └── app.js        # Custom JavaScript
│   └── uploads/          # File upload directory
└── data/                 # JSON data storage
```

## Customization Guide

### 1. Update Application Settings

Edit `app.py`:
- Change `app_name` in the context processor
- Update routes and business logic
- Add your database models
- Configure additional middleware

### 2. Customize Templates

- **Base Template**: Edit `templates/base.html` for navigation and layout
- **Home Page**: Modify `templates/index.html`
- **Dashboard**: Update `templates/dashboard.html` with your metrics
- **Styling**: Add custom CSS to `static/css/style.css`

### 3. Add Your Business Logic

```python
# Example: Add a new route
@app.route('/my-feature')
def my_feature():
    # Your logic here
    return render_template('my-feature.html')

# Example: Add API endpoint
@app.route('/api/my-data', methods=['GET', 'POST'])
def handle_my_data():
    if request.method == 'POST':
        # Handle data submission
        data = request.get_json()
        # Process and save data
        return jsonify({'success': True})
    else:
        # Return data
        return jsonify({'data': []})
```

### 4. Database Integration

For production applications, replace JSON storage with a proper database:

```python
# Install database packages
pip install Flask-SQLAlchemy Flask-Migrate

# Add to app.py
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate

db = SQLAlchemy(app)
migrate = Migrate(app, db)

# Define models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
```

## API Endpoints

The template includes these API endpoints:

- `GET /api/health` - Health check
- `POST /api/upload` - File upload
- `GET/POST /api/data` - Generic data handling

## Environment Variables

Key environment variables (see `.env.example`):

- `SECRET_KEY` - Flask secret key (change in production!)
- `FLASK_DEBUG` - Enable debug mode
- `DATABASE_URL` - Database connection string
- `PORT` - Server port (default: 5000)

## Security Considerations

- ✅ Change the `SECRET_KEY` in production
- ✅ Use HTTPS in production
- ✅ Validate all file uploads
- ✅ Sanitize user inputs
- ✅ Use environment variables for sensitive data
- ✅ Enable CSRF protection for forms
- ✅ Set secure cookie flags in production

## Development Tips

### Adding New Pages

1. Create HTML template in `templates/`
2. Add route in `app.py`
3. Update navigation in `base.html`
4. Add any required CSS/JS

### File Uploads

The template supports file uploads with:
- Size validation (16MB max)
- Type validation
- Secure filename handling
- Upload progress indication

### AJAX Requests

Use the built-in JavaScript utilities:

```javascript
// Make AJAX request
App.makeAjaxRequest('/api/my-endpoint', 'POST', {data: 'value'});

// Show alert
App.showAlert('Success message', 'success');

// Show loading
App.showLoading(true);
```

## Deployment

### Production Checklist

- [ ] Change `SECRET_KEY`
- [ ] Set `FLASK_ENV=production`
- [ ] Configure proper database
- [ ] Set up reverse proxy (nginx)
- [ ] Enable HTTPS
- [ ] Configure logging
- [ ] Set up monitoring
- [ ] Configure backups

### Recommended Production Stack

- **Web Server**: Gunicorn + Nginx
- **Database**: PostgreSQL or MySQL
- **Caching**: Redis
- **Monitoring**: Prometheus + Grafana
- **Logging**: ELK Stack or similar

## Contributing

1. Fork the template
2. Create your feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This template is provided as-is for educational and development purposes. Customize as needed for your projects.

## Support

For issues and questions:
1. Check the code comments
2. Review Flask documentation
3. Check Bootstrap 5 documentation
4. Create an issue in your project repository

---

**Happy coding! 🚀**
