# Web Application Template - Python Dependencies

# Core web framework
Flask==2.3.3
Werkzeug==2.3.7

# Database support (uncomment as needed)
# SQLAlchemy==2.0.23        # ORM for database operations
# Flask-SQLAlchemy==3.1.1   # Flask integration for SQLAlchemy
# Flask-Migrate==4.0.5      # Database migrations

# Authentication (uncomment if needed)
# Flask-Login==0.6.3        # User session management
# Flask-WTF==1.2.1          # Form handling and CSRF protection
# WTForms==3.1.0            # Form validation

# API and data handling
requests>=2.31.0           # HTTP requests
beautifulsoup4>=4.12.0     # HTML parsing (for web scraping)

# File handling and utilities
Pillow>=10.0.0             # Image processing
python-dotenv>=1.0.0       # Environment variable management

# Development and testing (uncomment for development)
# pytest>=7.4.0            # Testing framework
# pytest-flask>=1.3.0      # Flask testing utilities
# black>=23.0.0             # Code formatting
# flake8>=6.0.0             # Code linting

# Production deployment (uncomment for production)
# gunicorn>=21.2.0          # WSGI server for production
# redis>=5.0.0              # Caching and session storage
# celery>=5.3.0             # Background task processing

# Optional enhancements
# colorama>=0.4.6           # Colored terminal output
# rich>=13.0.0              # Enhanced CLI formatting
# click>=8.1.0              # Command line interface creation
