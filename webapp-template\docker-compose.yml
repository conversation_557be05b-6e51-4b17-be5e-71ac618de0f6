# Web Application Template - Docker Compose Configuration
version: '3.8'

services:
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=your-production-secret-key-change-this
      - DATABASE_URL=sqlite:///app.db
    volumes:
      - ./data:/app/data
      - ./static/uploads:/app/static/uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add database service (uncomment if needed)
  # db:
  #   image: postgres:15
  #   environment:
  #     POSTGRES_DB: webapp
  #     POSTGRES_USER: webapp
  #     POSTGRES_PASSWORD: webapp_password
  #   volumes:
  #     - postgres_data:/var/lib/postgresql/data
  #   restart: unless-stopped

  # Optional: Add Redis for caching (uncomment if needed)
  # redis:
  #   image: redis:7-alpine
  #   restart: unless-stopped
  #   volumes:
  #     - redis_data:/data

# Optional: Define volumes (uncomment if using database services)
# volumes:
#   postgres_data:
#   redis_data:
