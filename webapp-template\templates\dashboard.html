{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-speedometer2"></i> Dashboard</h1>
    <button class="btn btn-primary" onclick="refreshData()">
        <i class="bi bi-arrow-clockwise"></i> Refresh
    </button>
</div>

<!-- Key Metrics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ data.total_items }}</h4>
                        <p class="mb-0">Total Items</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-collection fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ data.stats.active_users or 0 }}</h4>
                        <p class="mb-0">Active Users</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ data.stats.uploads or 0 }}</h4>
                        <p class="mb-0">Files Uploaded</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-cloud-upload fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ data.stats.pending or 0 }}</h4>
                        <p class="mb-0">Pending Tasks</p>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-clock fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Analytics -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-bar-chart"></i> Activity Chart</h5>
            </div>
            <div class="card-body">
                <canvas id="activityChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-pie-chart"></i> Distribution</h5>
            </div>
            <div class="card-body">
                <canvas id="distributionChart" width="200" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-clock-history"></i> Recent Activity</h5>
            </div>
            <div class="card-body">
                {% if data.recent_activity %}
                    <div class="list-group list-group-flush">
                        {% for activity in data.recent_activity %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-1">{{ activity.title }}</h6>
                                <p class="mb-1">{{ activity.description }}</p>
                                <small class="text-muted">{{ activity.timestamp | format_datetime }}</small>
                            </div>
                            <span class="badge bg-primary rounded-pill">{{ activity.type }}</span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-inbox fs-1"></i>
                        <p class="mt-2">No recent activity</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list-check"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="addNewItem()">
                        <i class="bi bi-plus-circle"></i> Add New Item
                    </button>
                    <button class="btn btn-outline-success" onclick="exportData()">
                        <i class="bi bi-download"></i> Export Data
                    </button>
                    <button class="btn btn-outline-info" onclick="generateReport()">
                        <i class="bi bi-file-text"></i> Generate Report
                    </button>
                    <button class="btn btn-outline-warning" onclick="showSettings()">
                        <i class="bi bi-gear"></i> Settings
                    </button>
                </div>
            </div>
        </div>
        
        <!-- System Status -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="bi bi-cpu"></i> System Status</h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">Server Status</small>
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: 100%"></div>
                    </div>
                    <small class="text-success">Online</small>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">Database</small>
                    <div class="progress">
                        <div class="progress-bar bg-success" style="width: 100%"></div>
                    </div>
                    <small class="text-success">Connected</small>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">Storage</small>
                    <div class="progress">
                        <div class="progress-bar bg-warning" style="width: 65%"></div>
                    </div>
                    <small class="text-warning">65% Used</small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- Chart.js for charts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Sample chart data - replace with your actual data
const activityData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [{
        label: 'Activity',
        data: [12, 19, 3, 5, 2, 3, 7],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1
    }]
};

const distributionData = {
    labels: ['Type A', 'Type B', 'Type C'],
    datasets: [{
        data: [30, 50, 20],
        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56']
    }]
};

// Initialize charts
const activityChart = new Chart(document.getElementById('activityChart'), {
    type: 'line',
    data: activityData,
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

const distributionChart = new Chart(document.getElementById('distributionChart'), {
    type: 'doughnut',
    data: distributionData,
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

// Dashboard functions
function refreshData() {
    // Implement data refresh logic
    location.reload();
}

function addNewItem() {
    // Implement add new item logic
    alert('Add new item functionality - customize this!');
}

function exportData() {
    // Implement data export logic
    alert('Export data functionality - customize this!');
}

function generateReport() {
    // Implement report generation logic
    alert('Generate report functionality - customize this!');
}

function showSettings() {
    // Implement settings modal or redirect
    alert('Settings functionality - customize this!');
}

// Auto-refresh dashboard every 5 minutes
setInterval(function() {
    fetch('/api/data')
        .then(response => response.json())
        .then(data => {
            // Update dashboard with new data
            console.log('Dashboard data refreshed');
        })
        .catch(error => console.error('Error refreshing data:', error));
}, 300000); // 5 minutes
</script>
{% endblock %}
