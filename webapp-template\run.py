#!/usr/bin/env python3
"""
Web Application Template - Development Runner
Simple script to run the application in development mode
"""

import os
import sys
from app import app

def main():
    """Run the Flask application"""
    print("=" * 50)
    print("Web Application Template")
    print("=" * 50)
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("⚠️  Warning: .env file not found!")
        print("   Copy .env.example to .env and configure your settings")
        print()
    
    # Get configuration
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    host = os.environ.get('HOST', '0.0.0.0')
    
    print(f"🚀 Starting server...")
    print(f"   Host: {host}")
    print(f"   Port: {port}")
    print(f"   Debug: {debug}")
    print(f"   URL: http://localhost:{port}")
    print()
    
    if debug:
        print("🔧 Running in DEBUG mode")
        print("   - Auto-reload enabled")
        print("   - Detailed error pages")
        print("   - Do NOT use in production!")
    else:
        print("🔒 Running in PRODUCTION mode")
        print("   - Debug disabled")
        print("   - Error logging enabled")
    
    print("=" * 50)
    print()
    
    try:
        app.run(
            host=host,
            port=port,
            debug=debug,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n👋 Shutting down server...")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
