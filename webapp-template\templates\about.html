{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <h1><i class="bi bi-info-circle"></i> About {{ app_name }}</h1>
        
        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title">Web Application Template</h5>
                <p class="card-text">
                    This is a comprehensive Flask web application template designed to help you quickly 
                    build modern web applications. It includes all the essential components and features 
                    you need to get started.
                </p>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="bi bi-gear"></i> Features</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle text-success"></i> Flask web framework</li>
                            <li><i class="bi bi-check-circle text-success"></i> Bootstrap 5 UI</li>
                            <li><i class="bi bi-check-circle text-success"></i> File upload system</li>
                            <li><i class="bi bi-check-circle text-success"></i> JSON data storage</li>
                            <li><i class="bi bi-check-circle text-success"></i> Error handling</li>
                            <li><i class="bi bi-check-circle text-success"></i> Responsive design</li>
                            <li><i class="bi bi-check-circle text-success"></i> API endpoints</li>
                            <li><i class="bi bi-check-circle text-success"></i> Dashboard with charts</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h5><i class="bi bi-tools"></i> Technologies</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><i class="bi bi-arrow-right text-primary"></i> <strong>Backend:</strong> Python Flask</li>
                            <li><i class="bi bi-arrow-right text-primary"></i> <strong>Frontend:</strong> Bootstrap 5</li>
                            <li><i class="bi bi-arrow-right text-primary"></i> <strong>Icons:</strong> Bootstrap Icons</li>
                            <li><i class="bi bi-arrow-right text-primary"></i> <strong>Charts:</strong> Chart.js</li>
                            <li><i class="bi bi-arrow-right text-primary"></i> <strong>Storage:</strong> JSON files</li>
                            <li><i class="bi bi-arrow-right text-primary"></i> <strong>Deployment:</strong> Docker ready</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header bg-info text-white">
                <h5><i class="bi bi-book"></i> Getting Started</h5>
            </div>
            <div class="card-body">
                <h6>1. Install Dependencies</h6>
                <pre class="bg-light p-2 rounded"><code>pip install -r requirements.txt</code></pre>
                
                <h6 class="mt-3">2. Run the Application</h6>
                <pre class="bg-light p-2 rounded"><code>python app.py</code></pre>
                
                <h6 class="mt-3">3. Customize for Your Needs</h6>
                <ul>
                    <li>Modify <code>app.py</code> to add your business logic</li>
                    <li>Update templates in the <code>templates/</code> folder</li>
                    <li>Add custom CSS in <code>static/css/style.css</code></li>
                    <li>Add custom JavaScript in <code>static/js/app.js</code></li>
                    <li>Configure environment variables in <code>.env</code></li>
                </ul>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header bg-warning text-dark">
                <h5><i class="bi bi-exclamation-triangle"></i> Important Notes</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning" role="alert">
                    <strong>Security:</strong> Remember to change the secret key in production!
                </div>
                <div class="alert alert-info" role="alert">
                    <strong>Database:</strong> This template uses JSON files for simplicity. 
                    For production apps, consider using a proper database like PostgreSQL or MySQL.
                </div>
                <div class="alert alert-success" role="alert">
                    <strong>Deployment:</strong> The template includes Docker configuration for easy deployment.
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header bg-secondary text-white">
                <h5><i class="bi bi-folder"></i> Project Structure</h5>
            </div>
            <div class="card-body">
                <pre class="bg-light p-3 rounded"><code>webapp-template/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── .env                  # Environment variables (create this)
├── Dockerfile            # Docker configuration
├── docker-compose.yml    # Docker Compose setup
├── templates/            # HTML templates
│   ├── base.html         # Base template
│   ├── index.html        # Home page
│   ├── dashboard.html    # Dashboard page
│   ├── about.html        # About page
│   └── errors/           # Error pages
├── static/               # Static files
│   ├── css/              # Custom CSS
│   ├── js/               # Custom JavaScript
│   └── uploads/          # File uploads
└── data/                 # JSON data storage</code></pre>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="{{ url_for('index') }}" class="btn btn-primary btn-lg">
                <i class="bi bi-house"></i> Back to Home
            </a>
            <a href="{{ url_for('dashboard') }}" class="btn btn-success btn-lg ms-2">
                <i class="bi bi-speedometer2"></i> View Dashboard
            </a>
        </div>
    </div>
</div>
{% endblock %}
