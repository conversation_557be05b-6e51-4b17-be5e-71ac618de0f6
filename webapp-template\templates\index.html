{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
            <h1 class="display-4">Welcome to {{ app_name }}</h1>
            <p class="lead">This is your web application template. Customize it to build amazing things!</p>
            <hr class="my-4">
            <p>Current time: {{ current_time.strftime('%Y-%m-%d %H:%M:%S') }}</p>
            <a class="btn btn-light btn-lg" href="{{ url_for('dashboard') }}" role="button">
                <i class="bi bi-speedometer2"></i> Go to Dashboard
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="bi bi-speedometer2 text-primary"></i> Dashboard
                </h5>
                <p class="card-text">View your application dashboard with key metrics and recent activity.</p>
                <a href="{{ url_for('dashboard') }}" class="btn btn-primary">View Dashboard</a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="bi bi-cloud-upload text-success"></i> File Upload
                </h5>
                <p class="card-text">Upload files to your application. Supports images, documents, and more.</p>
                <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#uploadModal">
                    Upload Files
                </button>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="bi bi-info-circle text-info"></i> About
                </h5>
                <p class="card-text">Learn more about this application and how to customize it for your needs.</p>
                <a href="{{ url_for('about') }}" class="btn btn-info">Learn More</a>
            </div>
        </div>
    </div>
</div>

<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Upload File</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="fileInput" class="form-label">Choose file</label>
                        <input type="file" class="form-control" id="fileInput" name="file" required>
                        <div class="form-text">Supported formats: PNG, JPG, GIF, PDF, TXT, CSV (max 16MB)</div>
                    </div>
                    <div class="progress d-none" id="uploadProgress">
                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="uploadBtn">Upload</button>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-12">
        <h3>Quick Stats</h3>
        <div class="row">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h2 class="text-primary">0</h2>
                        <p class="card-text">Total Items</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h2 class="text-success">0</h2>
                        <p class="card-text">Active Users</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h2 class="text-info">0</h2>
                        <p class="card-text">Files Uploaded</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h2 class="text-warning">{{ current_time.strftime('%d') }}</h2>
                        <p class="card-text">Day of Month</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// File upload functionality
document.getElementById('uploadBtn').addEventListener('click', function() {
    const form = document.getElementById('uploadForm');
    const fileInput = document.getElementById('fileInput');
    const progressBar = document.getElementById('uploadProgress');
    
    if (!fileInput.files[0]) {
        alert('Please select a file');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    
    // Show progress bar
    progressBar.classList.remove('d-none');
    
    fetch('/api/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('File uploaded successfully!');
            bootstrap.Modal.getInstance(document.getElementById('uploadModal')).hide();
            form.reset();
        } else {
            alert('Upload failed: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Upload failed');
    })
    .finally(() => {
        progressBar.classList.add('d-none');
    });
});
</script>
{% endblock %}
